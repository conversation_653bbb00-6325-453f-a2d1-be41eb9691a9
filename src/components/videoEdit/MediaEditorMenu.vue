<template>
  <div class="media-editor-menu" v-if="currentShot">
    <div class="media-editor-image" v-if="currentShot.type === 'image'">
      <!-- 添加图片动画菜单 -->
      <div class="menu-item animation-menu">
        <!-- 使用DropdownPanel组件 -->
        <DropdownPanel v-model="isAnimationPanelVisible" position="bottom" align="center" :min-width="100">
          <!-- 将触发按钮放在trigger插槽中 -->
          <template #trigger>
            <div class="menu-button">
              <el-icon>
                <VideoCamera />
              </el-icon>
              <span>{{ currentShot.movement ? `动画-${getMovementName(currentShot.movement)}` : '动画-无' }}</span>
            </div>
          </template>

          <!-- 动画选项列表 -->
          <div class="animation-options">
            <!-- <div class="animation-option-title">动画效果</div> -->
            <div v-for="option in animationOptions" :key="option.value" class="animation-option-item"
              :class="{ 'active': currentShot.movement === option.value }" @click="selectAnimation(option.value)">
              <div class="option-item-text">{{ option.label }}</div>
            </div>
          </div>
        </DropdownPanel>
      </div>

      <!-- 添加图片填充方式菜单 -->
      <div class="menu-item object-fit-menu">
        <DropdownPanel v-model="isObjectFitPanelVisible" position="bottom" align="center" :min-width="100">
          <template #trigger>
            <div class="menu-button">
              <el-icon>
                <Picture />
              </el-icon>
              <span>{{ `填充-${getObjectFitName(currentShot.displayType || 'contain')}` }}</span>
            </div>
          </template>

          <!-- 填充方式选项列表 -->
          <div class="object-fit-options">
            <!-- <div class="animation-option-title">填充方式</div> -->
            <div v-for="option in objectFitOptions" :key="option.value" class="animation-option-item"
              :class="{ 'active': (currentShot.displayType || 'contain') === option.value }"
              @click="selectObjectFit(option.value)">
              <div class="option-item-text">{{ option.label }}</div>
            </div>
          </div>
        </DropdownPanel>
      </div>

      <!-- 添加智能修图按钮 -->
      <div class="menu-item inpainting-menu">
        <div class="menu-button" @click="openLocalInpainting">
          <el-icon>
            <Brush />
          </el-icon>
          <span>智能修图</span>
        </div>
      </div>

      <div class="menu-item inpainting-menu" v-if="currentShot.type === 'image'"> 
        <div class="menu-button" @click="handlePitchVideo">
          <el-icon>
            <Brush />
          </el-icon>
          <span>对口型(Beta)</span>
        </div>
      </div>
      
    </div>
    <div class="media-editor-video" v-else-if="currentShot.type === 'video'">
      <!-- 添加视频填充方式菜单 -->
      <div class="menu-item object-fit-menu">
        <DropdownPanel v-model="isVideoObjectFitPanelVisible" position="bottom" align="center" :min-width="100">
          <template #trigger>
            <div class="menu-button">
              <el-icon>
                <VideoCamera />
              </el-icon>
              <span>{{ `填充-${getObjectFitName(currentShot.displayType || 'contain')}` }}</span>
            </div>
          </template>

          <!-- 填充方式选项列表 -->
          <div class="object-fit-options">
            <!-- <div class="animation-option-title">填充方式</div> -->
            <div v-for="option in objectFitOptions" :key="option.value" class="animation-option-item"
              :class="{ 'active': (currentShot.displayType || 'contain') === option.value }"
              @click="selectObjectFit(option.value)">
              <div class="option-item-text">{{ option.label }}</div>
            </div>
          </div>
        </DropdownPanel>
      </div>

      <div class="menu-item object-fit-menu">

        <!-- 暂未实现 -->
        <div class="menu-button" @click="handleExtendVideo">
          <el-icon>
            <VideoCamera />
          </el-icon>
          <span>延长新分镜</span>
        </div>
      </div>

      <!-- 暂未实现 -->
      <!-- <div class="menu-item object-fit-menu">
        <div class="menu-button" @click="handlePitchVideo">
          <el-icon>
            <VideoCamera />
          </el-icon>
          <span>对口型</span>
        </div>
      </div> -->
    </div>

    <!-- 局部重绘组件 -->
    <LocalImageInpainting
      v-if="isLocalInpaintingVisible"
      :visible="isLocalInpaintingVisible"
      :imageUrl="currentShot.imageUrl"
      :shotId="currentShot.id"
      :conversationId="currentShot.conversationId || 'inpainting'"
      @close="closeLocalInpainting"
      @update="handleInpaintingUpdate"
    />

    <!-- 对口型确认对话框 -->
    <LipSyncConfirmDialog
      v-if="isLipSyncDialogVisible"
      :visible="isLipSyncDialogVisible"
      :currentShot="currentShot"
      @close="closeLipSyncDialog"
      @confirm="handleLipSyncConfirm"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { VideoCamera, Picture, ZoomIn, ZoomOut, ArrowRight, ArrowLeft, ArrowUp, ArrowDown, Close, Brush } from '@element-plus/icons-vue';
import DropdownPanel from '@/components/parent/DropdownPanel.vue';
import LocalImageInpainting from './LocalImageInpainting.vue';
import LipSyncConfirmDialog from './LipSyncConfirmDialog.vue';
import { extendVideo, setMaterialLipSync } from '@/api/auth';
import { ElLoading, ElMessage } from 'element-plus';

const props = defineProps({
  currentShot: {
    type: Object,
    default: () => null
  }
});

const emit = defineEmits(['update','refresh']);

// 动画面板相关状态
const isAnimationPanelVisible = ref(false);
const animationOptions = [
  { label: '无动画', value: '', icon: 'Close' },
  { label: '推进镜头', value: 'zoom-in', icon: 'ZoomIn' },
  { label: '拉远镜头', value: 'zoom-out', icon: 'ZoomOut' },
  { label: '向右平移', value: 'pan-right', icon: 'ArrowRight' },
  { label: '向左平移', value: 'pan-left', icon: 'ArrowLeft' },
  { label: '向上平移', value: 'tilt-up', icon: 'ArrowUp' },
  { label: '向下平移', value: 'tilt-down', icon: 'ArrowDown' },
];

// 填充方式面板相关状态
const isObjectFitPanelVisible = ref(false);
const isVideoObjectFitPanelVisible = ref(false);
const objectFitOptions = [
  { label: '适应全屏', value: 'contain', icon: 'Picture' },
  { label: '铺满全屏', value: 'cover', icon: 'Picture' },
  { label: '拉伸填充', value: 'fill', icon: 'Picture' },
  // { label: '保持比例', value: 'scale-down', icon: 'Picture' },
  // { label: '无缩放', value: 'none', icon: 'Picture' },
];

// 局部重绘相关状态
const isLocalInpaintingVisible = ref(false);

// 选择动画效果
const selectAnimation = (animationType) => {
  if (props.currentShot) {
    // 通过事件通知父组件更新分镜
    emit('update', {
      movement: animationType
    });

    // 关闭面板
    isAnimationPanelVisible.value = false;
  }
};

// 获取动画类型的显示名称
const getMovementName = (movement) => {
  const option = animationOptions.find(opt => opt.value === movement);
  return option ? option.label : '添加动画';
};

// 选择填充方式
const selectObjectFit = (objectFitType) => {
  if (props.currentShot) {
    // 通过事件通知父组件更新分镜
    emit('update', {
      displayType: objectFitType
    });

    // 关闭面板
    isObjectFitPanelVisible.value = false;
    isVideoObjectFitPanelVisible.value = false;
  }
};

// 获取填充方式的显示名称
const getObjectFitName = (displayType) => {
  const option = objectFitOptions.find(opt => opt.value === displayType);
  return option ? option.label : '包含';
};

// 打开局部重绘组件
const openLocalInpainting = () => {
  isLocalInpaintingVisible.value = true;
};

// 关闭局部重绘组件
const closeLocalInpainting = () => {
  isLocalInpaintingVisible.value = false;

  // 通过事件通知父组件更新分镜
  emit('refresh');
};

// 处理局部重绘更新
const handleInpaintingUpdate = (updatedImage) => {
  if (props.currentShot) {
    // 通过事件通知父组件更新分镜
    emit('update', {
      imageUrl: updatedImage
    });
  }
};

// 关闭对口型对话框
const closeLipSyncDialog = () => {
  isLipSyncDialogVisible.value = false;
};

// 处理对口型确认
const handleLipSyncConfirm = async () => {
  if (!props.currentShot) {
    ElMessage.error('当前分镜信息不完整');
    return;
  }

  // 关闭对话框
  isLipSyncDialogVisible.value = false;

  // 显示loading
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在处理对口型...',
    background: 'rgba(0, 0, 0, 0.7)',
  });

  try {
    // 构建API参数
    const params = {
      shotId: props.currentShot.id
    };

    console.log('对口型处理参数:', params);

    // 调用对口型API
    const response = await setMaterialLipSync(params);

    if (response.success) {
      ElMessage.success('对口型处理已开始，请稍后查看结果');
      // 通知父组件刷新
      emit('refresh');
    } else {
      ElMessage.error(response.errMessage || '对口型处理失败');
    }
  } catch (error) {
    console.error('对口型处理失败:', error);
    ElMessage.error('对口型处理失败，请重试');
  } finally {
    // 关闭loading
    loadingInstance.close();
  }
};

const setExtendVideo = async (duration) => {
  if (props.currentShot) {
    const lastFrameImageUrl = props.currentShot.videoUrl+'?x-oss-process=video/snapshot,t_'+duration+',f_jpg';
    const shotId = props.currentShot.id;

    console.log('延长视频参数:', lastFrameImageUrl, shotId);

    try {
      const response = await extendVideo({
        lastFrameImageUrl: lastFrameImageUrl,
        shotId: shotId
      });
      console.log('延长视频响应:', response);

      if (response.success) {
        // 通过事件通知父组件更新分镜
        emit('refresh');
        ElMessage.success('视频延长成功');
        return true;
      } else {
        ElMessage.error(response.errMessage || '延长视频失败');
        return false;
      }
    } catch (error) {
      console.error('延长视频API调用失败:', error);
      ElMessage.error(error.message || '延长视频失败');
      return false;
    }
  }
  return false;
}

// 对口型对话框状态
const isLipSyncDialogVisible = ref(false);

// 视频配音
const handlePitchVideo = async () => {

  // ElMessage.info('功能正在开发中...');
  // return;

  // 打开对口型确认对话框
  isLipSyncDialogVisible.value = true;
}

// 视频延长
const handleExtendVideo = async () => {
  if (props.currentShot) {
    // loading
    const loadingInstance = ElLoading.service({
      lock: true,
      text: '自动延长为新的分镜...',
      background: 'rgba(0, 0, 0, 0.7)',
    });

    try {
      // 获取当前分镜视频 URL 的时长
      const videoUrl = props.currentShot.videoUrl;
      // 获取视频时长
      const video = document.createElement('video');
      video.src = videoUrl;
      video.load();

      video.addEventListener('loadedmetadata', async () => {
        try {
          const duration = Math.floor(video.duration * 1000) - 41;
          console.log('视频时长:', duration);

          // 设置视频帧图片时间
          const success = await setExtendVideo(duration);

          // 释放内存，删除视频元素
          video.pause();
          video.remove();

          // 关闭 loading
          loadingInstance.close();
        } catch (error) {
          console.error('处理视频延长时出错:', error);
          ElMessage.error('处理视频延长时出错');

          // 释放内存，删除视频元素
          video.pause();
          video.remove();

          // 关闭 loading
          loadingInstance.close();
        }
      });

      // 视频错误处理
      video.addEventListener('error', () => {
        console.error('视频加载失败');
        ElMessage.error('视频加载失败');

        // 释放内存，删除视频元素
        video.pause();
        video.remove();

        // 关闭 loading
        loadingInstance.close();
      });

      // 视频播放结束
      video.addEventListener('ended', () => {
        console.log('视频播放结束');
      });

    } catch (error) {
      console.error('初始化视频延长时出错:', error);
      ElMessage.error('初始化视频延长时出错');
      loadingInstance.close();
    }
  }
};
</script>

<style scoped>
/* 媒体编辑菜单样式 */
.media-editor-menu {
  padding: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 32px;
}

.media-editor-image,
.media-editor-video {
  display: flex;
  flex-wrap: wrap;
  border-radius: 16px;
  background-color: #ffffff;
  border: 1px solid #e4e7ed;
  padding: 4px;
}

body.dark .media-editor-image,
body.dark .media-editor-video {
  background-color: var(--bg-secondary-video);
  border-color: var(--border-color);
}

.menu-item {
  position: relative;
}

.menu-button.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.menu-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s;
  color: #6a6a6a;
}

.menu-button:hover {
  background-color: #ecf5ff;
  color: #409eff;
}

body.dark .menu-button {
  color: var(--text-secondary);
}

body.dark .menu-button:hover {
  background-color: var(--bg-quaternary);
  color: var(--primary-color);
}

/* 动画选项样式 */
.animation-options,
.object-fit-options {
  padding: 10px;
  min-width: 100px;
}

.animation-option-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
  color: #606266;
}

body.dark .animation-option-title {
  color: var(--text-primary);
  border-bottom-color: var(--border-color);
}

.option-item-text {
  text-align: center;
  width: 100%;
}

.animation-option-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
}

.animation-option-item:hover {
  background-color: #f5f7fa;
}

.animation-option-item.active {
  background-color: #ecf5ff;
  color: #409eff;
}

body.dark .animation-option-item:hover {
  background-color: var(--bg-tertiary);
}

body.dark .animation-option-item.active {
  background-color: var(--bg-quaternary);
  color: var(--primary-color);
}
</style>