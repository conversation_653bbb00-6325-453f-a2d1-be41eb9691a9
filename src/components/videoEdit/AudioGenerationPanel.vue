<template>
  <div class="audio-generation-panel">
    <!-- 当前音频列表 -->
    <div class="panel-section">
      <div v-if="shotData.audios && shotData.audios.length > 0" class="voice-list">
        <draggable v-model="internalVoices" class="voice-draggable-list" item-key="id" :animation="150"
          ghost-class="ghost-voice" drag-class="dragging-voice" handle=".voice-draggable-list">
          <template #item="{ element: voice, index }">
            <div class="voice-item">
              <!-- 播放按钮 - 最左侧 -->
              <div class="voice-play-section">
                <div class="voice-play" :class="{ 'playing': isPlaying(voice.voiceId) }" @click.stop="playVoice(voice)">
                  <VideoPause v-if="isPlaying(voice.voiceId)" class="play-icon" />
                  <VideoPlay v-else class="play-icon" />
                  <span class="wave-animation" v-if="isPlaying(voice.voiceId)">
                    <span class="wave-bar"></span>
                    <span class="wave-bar"></span>
                    <span class="wave-bar"></span>
                    <span class="wave-bar"></span>
                  </span>
                </div>
              </div>

              <!-- 音频信息 - 中间区域 -->
              <div class="voice-info" @click="updateVoice(voice)">
                <div class="voice-header">
                  <div class="voice-yinxiao-tag" v-if="voice.audioType == 2">音效</div>
                  <div class="voice-name" v-else>{{voices.find(v => v.id == voice.voiceId)?.name || (index + 1)}}</div>
                  <div class="voice-effect" :class="getVolumeClass(voice.volume)"
                       @mouseenter="showVolumeControlOnHover(index)"
                       @mouseleave="hideVolumeControlOnHover(index)">
                    <div class="volume-indicator">
                      <div class="volume-bar">
                        <div class="volume-fill" :style="{ width: (voice.volume * 100) + '%' }"></div>
                      </div>
                      <span class="volume-text">{{ Math.round(voice.volume * 100) }}%</span>
                    </div>
                    <!-- 音量控制滑块 -->
                    <div v-if="showVolumeControl === index" class="volume-control-popup" @click.stop
                         @mouseenter="keepVolumeControlVisible(index)"
                         @mouseleave="hideVolumeControlOnHover(index)">
                      <div class="volume-slider-container">
                        <el-slider
                          v-model="tempVolume"
                          :min="0"
                          :max="150"
                          :step="1"
                          :show-tooltip="true"
                          :format-tooltip="formatVolumeTooltip"
                          @input="onVolumeChange(voice, $event)"
                          @change="onVolumeChangeComplete(voice, $event)"
                          class="volume-slider"
                        />
                        <!-- <div class="volume-percentage">{{ Math.round(tempVolume) }}%</div> -->
                      </div>
                    </div>
                  </div>
                </div>
                <div class="voice-narration" v-if="voice.audioType == 1">{{ voice.text }}</div>
              </div>

              <!-- 右侧操作按钮区域 -->
              <div class="voice-actions">
                <!-- 操作按钮组 -->
                <div class="voice-action-buttons">

                  <!-- 菜单按钮 -->
                  <DropdownPanel :modelValue="showMenuSelector === index"
                    @update:modelValue="(value) => handleMenuToggle(value, index)" position="bottom" align="center"
                    closeOnClickOutside :width="60" :maxHeight="420" :zIndex="1200"
                    :dropdown-id="`audio-voice-menu-selector-${voice.id}-${index}`" :portal="false">
                    <!-- 触发器插槽 -->
                    <template #trigger>
                      <div class="voice-delete">
                        <el-icon class="menu-icon">
                          <Menu />
                        </el-icon>
                      </div>
                    </template>

                    <!-- 下拉内容插槽 -->
                    <div class="voice-split-actions">
                      <div class="voice-split-text" @click.stop="downloadAudio(voice); showMenuSelector = null">下载</div>
                      <div class="voice-split-text"
                        @click.stop="splitText(voice.text, voice.voiceId, index); showMenuSelector = null"
                        v-if="voice.audioType == 1">拆分</div>
                      <div class="voice-split-text" @click.stop="removeVoice(index); showMenuSelector = null">删除</div>
                    </div>
                  </DropdownPanel>

                  <!-- 拖拽按钮 -->
                  <!-- <div class="drag-handle" @click.stop>
                    <el-icon class="drag-icon">
                      <Operation />
                    </el-icon>
                  </div> -->
                </div>

                <!-- 音频时长 - 右下角 -->
                <div class="voice-duration">{{ formatDuration(voice.audioDuration) }}</div>
              </div>
            </div>
          </template>
        </draggable>
      </div>
      <div v-else class="empty-voice-list">
        <el-icon class="empty-icon">
          <Microphone />
        </el-icon>
        <div class="empty-text">无音频</div>
      </div>
    </div>

    <!-- 添加音频按钮 -->
    <div class="add-audio-button" @click="addVoice">
      <div class="add-audio-content">
        <el-icon>
          <Plus />
        </el-icon>
        <span>添加音频</span>
      </div>
    </div>

    <!-- 音频生成弹窗 -->
    <AudioGenerationDialog v-model="showAudioGenerationDialog" :shot="shot" :voices="voices"
      :isLoadingVoices="isLoadingVoices" :voiceToCopy="voiceToCopy" @refresh-canvas="emit('refresh-canvas')"
      @refresh-voices="emit('refresh-voices')" @update:modelValue="handleDialogClose" />

  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { Microphone, VideoPause, VideoPlay, Menu, Plus } from '@element-plus/icons-vue';
import { ElMessage, ElLoading } from 'element-plus';
import draggable from 'vuedraggable';
import DropdownPanel from '../parent/DropdownPanel.vue';

import AudioGenerationDialog from './AudioGenerationDialog.vue';
import { createCanvasShotAudio, deleteCanvasShotAudio, updateCanvasShotAudioOrder, updateCanvasShotAudio, updateShotVolume } from '@/api/auth.js';

// 组件属性
const props = defineProps({
  shot: {
    type: Object,
    required: true
  },
  voices: {
    type: Array,
    default: () => []
  },
  isLoadingVoices: {
    type: Boolean,
    default: false
  }
});

// 事件
const emit = defineEmits(['refresh-canvas', 'refresh-voices']);

// 本地数据副本
const shotData = ref({ ...props.shot });

// 弹窗显示状态
const showAudioGenerationDialog = ref(false);

// 要复制的音频数据
const voiceToCopy = ref(null);

// 音量控制相关
const showVolumeControl = ref(null); // 当前显示音量控制的音频索引
const tempVolume = ref(100); // 临时音量值

// 内部维护一个voices的副本，用于拖拽排序
const internalVoices = computed({
  get: () => shotData.value.audios || [],
  set: async (value) => {
    // 更新本地数据
    shotData.value = {
      ...shotData.value,
      audios: value
    };

    // 如果没有分镜ID或者音频数量小于2，不需要更新顺序
    if (!shotData.value.id || value.length < 2) {
      return;
    }

    try {
      // 构建API参数
      const params = {
        shotId: shotData.value.id,
        audioOrders: value.map((audio, index) => ({
          audioId: audio.id,
          sortOrder: index + 1
        }))
      };

      // 调用API更新音频顺序
      const response = await updateCanvasShotAudioOrder(params);

      if (response.success) {
        console.log('音频顺序更新成功');
        // 通知父组件更新画布详情
        emit('refresh-canvas');
      } else {
        console.error('音频顺序更新失败:', response.errMessage);
        ElMessage.error(`音频顺序更新失败: ${response.errMessage}`);
      }
    } catch (error) {
      console.error('更新音频顺序异常:', error);
      ElMessage.error('更新音频顺序出错，请稍后再试');
    }
  }
});

// 菜单选择器状态
const showMenuSelector = ref(null); // 改为存储当前展开的音频项的索引

// 当前播放的音频元素和ID
const currentAudio = ref(null);
const currentPlayingId = ref(null);

// 判断是否正在播放特定音频
const isPlaying = (voiceId) => {
  return currentPlayingId.value === voiceId && currentAudio.value && !currentAudio.value.paused;
};

const addVoice = () => {
  voiceToCopy.value = {
    text: "",
    tone: [],
  };
  showAudioGenerationDialog.value = true
};

// 修改音频 - 打开弹窗并填充数据，或直接调用修改音频API
const updateVoice = (voice) => {

  if (voice.audioType != 1) {
    return;
  }

  // 设置要复制的音频数据，包含音频ID用于更新
  voiceToCopy.value = {
    audioId: voice.id,  // 添加音频ID
    text: voice.text,
    voiceId: voice.voiceId,
    speed: voice.rate || 1.0,
    vol: voice.ttsVolume || 1.0,
    pitch: voice.pitch || 0,
    emotion: voice.emotion,
    tone: voice.tone || []
  };

  // 打开弹窗
  showAudioGenerationDialog.value = true;

  // 复制内容到粘贴板
  // navigator.clipboard.writeText(voice.text);
  // ElMessage.success('已复制内容到剪贴板');
};

const downloadAudio = async (voice) => {
  if (!voice || !voice.audioUrl) {
    ElMessage.error('音频链接不存在');
    return;
  }

  try {
    // ElMessage.info('准备下载音频，请稍候...');

    // 使用fetch API获取视频文件
    const response = await fetch(voice.audioUrl);

    // 检查响应状态
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`);
    }

    // 将响应转换为blob
    const blob = await response.blob();

    // 创建一个blob URL
    const blobUrl = URL.createObjectURL(blob);

    // 创建一个临时的a标签用于下载
    const link = document.createElement('a');
    link.href = blobUrl;
    link.download = `${voice.id}_${voice.text}.mp3`;
    link.style.display = 'none';
    document.body.appendChild(link);

    // 触发点击下载
    link.click();

    // 清理
    setTimeout(() => {
      document.body.removeChild(link);
      URL.revokeObjectURL(blobUrl); // 释放blob URL
    }, 100);

    ElMessage.success('音频下载已开始');
  } catch (error) {
    console.error('下载音频出错:', error);
    ElMessage.error(`下载音频失败: ${error.message}`);

    // 如果fetch失败，尝试直接在新标签页中打开音频
    window.open(voice.audioUrl, '_blank');
  }
};


// 生成音频
const generateAudio = async (text, voiceId) => {
  if (!text) {
    ElMessage.warning('请输入文本');
    return;
  }

  if (!voiceId) {
    ElMessage.warning('请选择音色');
    return;
  }

  if (!shotData.value.id) {
    ElMessage.warning('分镜ID不存在，无法创建音频');
    return;
  }

  try {
    // 构建API参数 - 使用默认参数
    const params = {
      shotId: shotData.value.id,
      text: text,
      voiceId: voiceId,
      audioType: 1,
      speed: 1.0,    // 默认语速
      vol: 1.0,      // 默认音量
      pitch: 0,      // 默认语调
      emotion: undefined    // 默认情感
    };

    // 调用API创建音频
    const response = await createCanvasShotAudio(params);

    if (response.success) {
      ElMessage.success('音频生成成功');
      // 通知父组件更新画布详情
      emit('refresh-canvas');
      return true;
    } else {
      ElMessage.error(`音频生成失败: ${response.errMessage}`);
    }
  } catch (error) {
    console.error('生成音频异常:', error);
    ElMessage.error('生成音频出错，请稍后再试');
  }
  return false;
};

// 拆分文本
const splitText = async (text, voiceId, index) => {
  // 如果文本不包含标点符号，则不拆分
  if (!text.includes('，') && !text.includes('。') && !text.includes('！') && !text.includes('？') && !text.includes('｜')) {
    ElMessage.error('文本无法继续拆分');
    return;
  }

  // 全屏loading
  ElLoading.service({
    lock: true,
    text: '正在生成音频...',
    background: 'rgba(0, 0, 0, 0.7)',
  });

  //根据标点符号拆分文本
  const textArray = text.split(/[，。！？｜]/);
  if (textArray.length <= 1) {
    ElMessage.error('文本无法继续拆分');
    return;
  }
  let resSuccess = false;
  // 使用for...of循环确保按顺序执行异步操作
  // 如果拆分成功则删除当前音频
  for (const item of textArray) {
    if (item != '' && item != null) {
      resSuccess = await generateAudio(item, voiceId);
      if (!resSuccess) {
        break;
      }
    }
  }

  if (resSuccess) {
    setTimeout(() => {
      removeVoice(index);
    }, 300);
  }

  ElLoading.service().close();
};

// 播放音频
const playVoice = (voice) => {
  // 如果正在播放当前音频，则暂停
  if (isPlaying(voice.voiceId)) {
    currentAudio.value.pause();
    currentAudio.value = null;
    currentPlayingId.value = null;
    return;
  }

  // 如果有其他正在播放的音频，先停止
  if (currentAudio.value) {
    currentAudio.value.pause();
    currentAudio.value = null;
    currentPlayingId.value = null;
  }

  // 创建新的音频元素并播放
  if (voice.audioUrl) {
    const audioUrl = voice.audioUrl;
    const audio = new Audio(audioUrl);

    // 设置事件监听
    audio.addEventListener('play', () => {
      currentPlayingId.value = voice.voiceId;
    });

    audio.addEventListener('ended', () => {
      currentAudio.value = null;
      currentPlayingId.value = null;
    });

    audio.addEventListener('pause', () => {
      if (currentPlayingId.value === voice.voiceId) {
        currentPlayingId.value = null;
      }
    });

    audio.addEventListener('error', () => {
      console.error('音频播放错误');
      currentAudio.value = null;
      currentPlayingId.value = null;
    });

    audio.volume = voice.volume > 1 ? 1 : voice.volume; // 设置音量

    // 播放
    audio.play().catch(error => {
      console.error('播放失败:', error);
      currentAudio.value = null;
      currentPlayingId.value = null;
    });


    currentAudio.value = audio;
  }
};



// 删除音频
const removeVoice = async (index) => {
  const voice = internalVoices.value[index];

  // 检查是否有有效的音频ID
  if (!voice || !voice.id) {
    ElMessage.warning('音频ID不存在，无法删除');
    return;
  }
  await removeVoiceById(voice.id);
};

const removeVoiceById = async (id) => {
  console.log('removeVoiceById', id);

  try {
    // ElMessage.info('正在删除音频...');

    // 调用API删除音频
    const response = await deleteCanvasShotAudio(id);

    if (response.success) {
      ElMessage.success('音频删除成功');

      // 使用 nextTick 确保DOM更新完成后再刷新画布
      await nextTick();

      // 通知父组件更新画布详情
      emit('refresh-canvas');
    } else {
      ElMessage.error(`音频删除失败: ${response.errMessage}`);
    }
  } catch (error) {
    console.error('删除音频异常:', error);
    ElMessage.error('删除音频出错，请稍后再试');
  }
};

// 格式化时长（秒）
const formatDuration = (duration) => {
  if (!duration) return '00:00.00';

  const seconds = Math.floor(duration / 1000);
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  const milliseconds = Math.floor((duration % 1000) / 10);

  // return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}.${String(milliseconds).padStart(2, '0')}`;
  return `${String(remainingSeconds)}.${String(milliseconds).padStart(1, '0')}s`;
};

// 根据音量大小返回对应的CSS类
const getVolumeClass = (volume) => {
  if (volume >= 1.3) return 'volume-low';
  if (volume >= 1.1) return 'volume-medium';
  if (volume >= 0.8) return 'volume-high';
  if (volume >= 0.5) return 'volume-medium';
  if (volume >= 0.2) return 'volume-low';
  return 'volume-very-low';
};

// 切换音量控制显示
const toggleVolumeControl = (index) => {
  if (showVolumeControl.value === index) {
    showVolumeControl.value = null;
  } else {
    const voice = internalVoices.value[index];
    if (voice) {
      tempVolume.value = Math.round(voice.volume * 100);
      showVolumeControl.value = index;
    }
  }
};

// 鼠标悬停时显示音量控制
const showVolumeControlOnHover = (index) => {
  const voice = internalVoices.value[index];
  if (voice) {
    tempVolume.value = Math.round(voice.volume * 100);
    showVolumeControl.value = index;
  }
};

// 鼠标离开时隐藏音量控制
const hideVolumeControlOnHover = (index) => {
  // 添加一个小延迟，避免鼠标快速移动时闪烁
  setTimeout(() => {
    if (showVolumeControl.value === index) {
      showVolumeControl.value = null;
    }
  }, 100);
};

// 保持音量控制可见（当鼠标在弹窗上时）
const keepVolumeControlVisible = (index) => {
  // 确保弹窗保持显示
  if (showVolumeControl.value !== index) {
    const voice = internalVoices.value[index];
    if (voice) {
      tempVolume.value = Math.round(voice.volume * 100);
      showVolumeControl.value = index;
    }
  }
};

// 格式化音量提示
const formatVolumeTooltip = (value) => {
  return `${value}%`;
};

// 音量变化时的实时更新（拖拽过程中）
const onVolumeChange = (voice, volume) => {
  tempVolume.value = volume;
  // 实时更新显示，但不调用API
  voice.volume = volume / 100;
};

// 音量变化完成时的处理（拖拽结束）
const onVolumeChangeComplete = async (voice, volume) => {
  try {
    // 构建API参数
    const params = {
      shotId: shotData.value.id,
      type: voice.audioType === 2 ? 'soundEffect' : 'audio',
      audioId: voice.id,
      volume: volume / 100 // 转换为0-1.5范围
    };

    // 调用API更新音量
    await updateShotVolume(params);

    // 更新本地数据
    voice.volume = volume / 100;

    // 通知父组件刷新画布详情
    emit('refresh-canvas');

    // ElMessage.success('音量调整成功');
  } catch (error) {
    console.error('更新音量失败:', error);
    ElMessage.error('音量调整失败，请稍后再试');

    // 恢复原始音量
    const originalVolume = voice.volume * 100;
    tempVolume.value = originalVolume;
  }
};

// 处理菜单开关
const handleMenuToggle = (value, index) => {
  if (value) {
    // 打开新菜单时，确保关闭其他菜单
    showMenuSelector.value = index;
  } else {
    // 关闭菜单
    showMenuSelector.value = null;
  }
};

// 处理弹窗关闭
const handleDialogClose = (value) => {
  showAudioGenerationDialog.value = value;
  // 当弹窗关闭时，清空要复制的音频数据
  if (!value) {
    voiceToCopy.value = null;
  }
};



// 组件卸载前清理
onBeforeUnmount(() => {
  // 停止正在播放的音频
  if (currentAudio.value) {
    currentAudio.value.pause();
    currentAudio.value = null;
  }
});

// 监听外部shot变化
watch(() => props.shot, (newShot) => {
  shotData.value = { ...newShot };
}, { deep: true });
</script>

<style scoped>
.audio-generation-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 0 16px 16px 16px;
}

/* 添加音频按钮样式 */
.add-audio-button {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #e4e7ed;
  border-radius: 8px;
  min-height: 60px;
  margin-top: 4px;
  transition: all 0.3s ease-in-out;
  cursor: pointer;
  background-color: #fafafa;
}

.add-audio-button:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

body.dark .add-audio-button {
  border-color: #4c4d4f;
  background-color: rgba(204, 221, 255, .03);
}

body.dark .add-audio-button:hover {
  border-color: var(--primary-color);
  background-color: rgba(64, 158, 255, 0.1);
}

.add-audio-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  gap: 4px;
}

.add-audio-content .el-icon {
  font-size: 20px;
}

.add-audio-content span {
  font-size: 14px;
}

body.dark .add-audio-content {
  color: var(--text-secondary);
}

.generation-content-audio {
  /* height: 380px; */
  /* overflow-y: auto; */
  /* box-sizing: border-box; */
  /* overflow-x: hidden; */
}

.tab-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 12px;
}

.panel-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
  /* margin-bottom: 15px; */
}

.mt-10 {
  margin-top: auto;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.image-generation-textarea {
  background-color: #f5f7fa;
  border-radius: 8px;
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.1) !important;
  padding-bottom: 14px;
}

body.dark .image-generation-textarea {
  background-color: rgba(204, 221, 255, .06);
  box-shadow: 0 0 1px 0 rgba(0, 0, 0, 0.1) !important;
}

.form-item {
  margin-bottom: 0px;
  position: relative;
}

.form-label {
  font-size: 12px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 5px;
  text-align: left;
}

body.dark .form-label {
  color: var(--text-primary);
}

.panel-action-audio {
  padding: 0;
  display: flex;
  justify-content: center;
}

/* 生成按钮样式 */
.generation-button {
  width: 100%;
  justify-content: center;
  padding: 6px 12px;
  font-size: 15px;
}

.points-cost {
  margin-left: 5px;
  font-size: 14px;
  opacity: 0.9;
}

/* 返回按钮 */
.back-button {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  font-size: 14px;
  color: #ffffff;
  transition: color 0.3s;
  background-color: #706cefd7;
  padding: 8px 12px;
  border-radius: 18px;
}

.back-button:hover {
  background-color: #5855e9d7;
}

.back-button.is-generating {
  background-color: #5855e9d7;
  cursor: not-allowed;
  position: relative;
}

.loading-icon {
  animation: rotating 2s linear infinite;
}

.circular-icon {
  height: 16px;
  width: 16px;
  animation: rotating 2s linear infinite;
}

.path {
  stroke: white;
  stroke-width: 3;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-linecap: round;
  animation: dash 1.5s ease-in-out infinite;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }

  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

body.dark .back-button {
  background-color: rgba(112, 108, 239, 0.7);
}

body.dark .back-button:hover {
  background-color: rgba(88, 85, 233, 0.7);
}

/* Character count */
.character-count {
  position: absolute;
  right: 4px;
  bottom: 4px;
  text-align: right;
  font-size: 12px;
  color: #909399;
}

body.dark .character-count {
  color: var(--text-secondary);
}

/* Voice list styles */
.voice-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.voice-item {
  cursor: pointer;
  background-color: #f5f7fa;
  border-radius: 6px;
  padding: 8px;
  display: flex;
  align-items: flex-start;
  gap: 0px;
  position: relative;
  transition: all 0.2s ease;
  align-items: center;
}

body.dark .voice-item {
  background-color: rgba(204, 221, 255, .06);
}

.voice-item:hover {
  background-color: #eef2f8;
}

body.dark .voice-item:hover {
  background-color: rgba(204, 221, 255, .1);
}

.voice-item:hover .voice-split-actions {
  display: flex;
}

/* 播放按钮区域 */
.voice-play-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  flex-shrink: 0;
  margin-right: 8px;
}

.voice-info {
  flex: 1;
  min-width: 0;
  padding-top: 2px;
}

.voice-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.voice-yinxiao-tag {
  color: #409eff;
  border: 1px solid #409eff;
  opacity: 0.8;
  padding: 2px 4px;
  border-radius: 6px;
  font-size: 12px;
  margin-right: auto;
}

body.dark .voice-yinxiao-tag {
  color: #409eff;
  border: 1px solid #409eff;
}

/* 音量效果样式 */
.voice-effect {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-left: auto;
}

.volume-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 2px 6px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.volume-bar {
  width: 32px;
  height: 4px;
  background-color: #e4e7ed;
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.volume-fill {
  height: 100%;
  border-radius: 2px;
  transition: all 0.3s ease;
  position: relative;
}

.volume-fill::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 2px;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 1px;
}

.volume-text {
  font-size: 11px;
  font-weight: 500;
  min-width: 28px;
  text-align: center;
}

/* 不同音量级别的颜色 */
.voice-effect.volume-very-low .volume-fill {
  background-color: #f56c6c;
  box-shadow: 0 0 4px rgba(245, 108, 108, 0.3);
}

.voice-effect.volume-very-low .volume-text {
  color: #f56c6c;
}

.voice-effect.volume-low .volume-fill {
  background-color: #e6a23c;
  box-shadow: 0 0 4px rgba(230, 162, 60, 0.3);
}

.voice-effect.volume-low .volume-text {
  color: #e6a23c;
}

.voice-effect.volume-medium .volume-fill {
  background-color: #409eff;
  box-shadow: 0 0 4px rgba(64, 158, 255, 0.3);
}

.voice-effect.volume-medium .volume-text {
  color: #409eff;
}

.voice-effect.volume-high .volume-fill {
  background-color: #67c23a;
  box-shadow: 0 0 4px rgba(103, 194, 58, 0.3);
}

.voice-effect.volume-high .volume-text {
  color: #67c23a;
}

/* 悬停效果 */
.voice-item:hover .volume-indicator {
  background-color: rgba(255, 255, 255, 0.95);
  border-color: rgba(0, 0, 0, 0.15);
  transform: scale(1.05);
}

.voice-item:hover .volume-fill {
  animation: volumePulse 1.5s ease-in-out infinite;
}

@keyframes volumePulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 深色模式样式 */
body.dark .volume-indicator {
  background-color: rgba(204, 221, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.1);
}

body.dark .volume-bar {
  background-color: rgba(255, 255, 255, 0.1);
}

body.dark .voice-item:hover .volume-indicator {
  background-color: rgba(204, 221, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.2);
}

/* 音量控制弹窗样式 */
.volume-control-popup {
  position: absolute;
  top: 6px;
  right: 10px;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 14px;
  padding: 4px 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 100px;
  opacity: 1;
  transform: translateY(0);
  transition: all 0.2s ease-in-out;
}

/* .volume-control-popup::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: white;
} */

.volume-slider-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

.volume-slider {
}

.volume-percentage {
  font-size: 12px;
  font-weight: 500;
  color: #606266;
  text-align: center;
  min-width: 40px;
}

/* 深色模式下的音量控制弹窗 */
body.dark .volume-control-popup {
  background: #2d2d2d;
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

body.dark .volume-control-popup::after {
  border-top-color: #2d2d2d;
}

body.dark .volume-percentage {
  color: #e4e7ed;
}

/* 音量指示器可点击样式 */
.volume-indicator {
  cursor: pointer;
  transition: all 0.2s ease;
}

.volume-indicator:hover {
  transform: scale(1.05);
}



.voice-name {
  flex: 1;
  text-align: left;
  font-weight: 500;
  font-size: 14px;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}

body.dark .voice-name {
  color: var(--text-primary);
}

.voice-duration {
  font-size: 11px;
  color: #909399;
  text-align: center;
  font-weight: 500;
  min-width: 32px;
  margin-top: auto;
  align-self: flex-end;
}

.voice-narration {
  font-size: 12px;
  color: #606266;
  text-align: left;
}

body.dark .voice-narration {
  color: var(--text-primary);
}

.voice-split-actions {
  font-size: 12px;
  color: #878787bd;
  text-align: left;
  cursor: pointer;
  border-radius: 4px 4px 0px 0px;
  flex-direction: column;
  gap: 4px;
}

.voice-split-text {
  font-size: 12px;
  color: #878787;
  text-align: center;
  cursor: pointer;
  padding: 6px 0px;
  background-color: #ffffff;
  border-radius: 4px 4px 0px 0px;
}

body.dark .voice-split-text {
  color: var(--text-primary);
  background-color: rgba(204, 221, 255, .06);
}

.voice-split-text:hover {
  color: #409eff;
  background-color: #eef2f8;
}

body.dark .voice-split-text:hover {
  color: #409eff;
  background-color: rgba(204, 221, 255, .1);
}

.voice-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  flex-shrink: 0;
  padding-top: 2px;
  position: relative;
  height: 100%;
}

.voice-action-buttons {
  display: flex;
  align-items: flex-start;
  gap: 4px;
}

/* 拖拽按钮样式 */
.drag-handle {
  cursor: grab;
  color: #878787;
  transition: all 0.2s ease;
  text-align: center;
  font-size: 18px;
  height: 24px;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  background-color: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
}

.drag-handle:hover {
  transform: scale(1.05);
  color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.2);
  cursor: grab;
}

.drag-handle:active {
  cursor: grabbing;
  transform: scale(0.98);
}

/* 拖拽状态下的样式 */
.dragging-voice .drag-handle {
  cursor: grabbing !important;
}

/* 拖拽提示样式 */
.drag-handle::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 8px;
  background: linear-gradient(45deg, transparent 30%, rgba(64, 158, 255, 0.1) 50%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.drag-handle:hover::before {
  opacity: 1;
}

.drag-icon {
  width: 16px;
  height: 16px;
}

body.dark .drag-handle {
  color: #a0a0a0;
  background-color: rgba(204, 221, 255, 0.06);
  border-color: rgba(255, 255, 255, 0.1);
}

body.dark .drag-handle:hover {
  color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.2);
  cursor: grab;
}

body.dark .drag-handle::before {
  background: linear-gradient(45deg, transparent 30%, rgba(64, 158, 255, 0.15) 50%, transparent 70%);
}

.voice-delete {
  cursor: pointer;
  color: #878787;
  transition: all 0.2s ease;
  text-align: center;
  font-size: 18px;
  height: 24px;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  background-color: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.menu-icon {
  width: 16px;
  height: 16px;
}

.voice-delete:hover {
  transform: scale(1.05);
  color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.2);
}

body.dark .voice-delete {
  color: #a0a0a0;
  background-color: rgba(204, 221, 255, 0.06);
  border-color: rgba(255, 255, 255, 0.1);
}

body.dark .voice-delete:hover {
  color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.2);
}

.voice-item:hover .voice-delete {
  /* opacity: 1; */
}

/* 播放按钮样式 */
.voice-play {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  color: #606266;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
  position: relative;
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.voice-play:hover {
  transform: scale(1.05);
  color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.3);
}

.play-icon {
  width: 18px;
  height: 18px;
}

/* 播放状态 */
.voice-play.playing {
  color: #67c23a;
}

.voice-play.playing:hover {
  color: #85ce61;
  transform: scale(1.1);
}

/* 深色模式下的样式 */
body.dark .voice-play {
  color: #a0a0a0;
  background-color: rgba(204, 221, 255, 0.06);
  border-color: rgba(255, 255, 255, 0.1);
}

body.dark .voice-play:hover {
  color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.3);
}

body.dark .voice-play.playing {
  color: #67c23a;
}

body.dark .voice-play.playing:hover {
  color: #85ce61;
}

/* 波形动画 */
.wave-animation {
  position: absolute;
  left: 50%;
  top: -12px;
  transform: translateX(-50%);
  display: flex;
  align-items: flex-end;
  height: 12px;
  width: 14px;
  gap: 1px;
}

.wave-bar {
  width: 2px;
  background-color: #67c23a;
  border-radius: 1px;
  animation: waveAnimation 0.8s infinite ease-in-out;
}

.wave-bar:nth-child(1) {
  height: 4px;
  animation-delay: 0s;
}

.wave-bar:nth-child(2) {
  height: 7px;
  animation-delay: 0.2s;
}

.wave-bar:nth-child(3) {
  height: 5px;
  animation-delay: 0.4s;
}

.wave-bar:nth-child(4) {
  height: 6px;
  animation-delay: 0.6s;
}

@keyframes waveAnimation {

  0%,
  100% {
    transform: scaleY(0.6);
  }

  50% {
    transform: scaleY(1);
  }
}

.empty-voice-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
  background-color: #f5f7fa;
  border-radius: 6px;
}

body.dark .empty-voice-list {
  background-color: rgba(204, 221, 255, .06);
}

.empty-icon {
  font-size: 32px;
  color: #c0c4cc;
  margin-bottom: 10px;
}

.empty-text {
  font-size: 14px;
  color: #909399;
}

body.dark .empty-icon {
  color: #606266;
}

body.dark .empty-text {
  color: var(--text-secondary);
}

/* 拖拽相关样式 */
.voice-draggable-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.ghost-voice {
  opacity: 0.5;
  background: #c8ebfb;
  border: 1px dashed #409eff;
  transform: rotate(2deg);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

body.dark .ghost-voice {
  background: #2c3e50;
  border: 1px dashed var(--primary-color);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.dragging-voice {
  cursor: grabbing !important;
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.dragging-voice * {
  cursor: grabbing !important;
}

/* 拖拽提示文本 */
.drag-handle[title]:hover::after {
  content: attr(title);
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1001;
  pointer-events: none;
}

body.dark .drag-handle[title]:hover::after {
  background-color: rgba(255, 255, 255, 0.9);
  color: #303133;
}

/* 音色选择器样式 */
.voice-selector-item {
  /* margin-top: 15px; */
}

.voice-selector-dropdown {
  width: 100%;
}

.voice-selector-trigger {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 6px 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

body.dark .voice-selector-trigger {
  background-color: rgba(204, 221, 255, .06);
}

.voice-selector-trigger:hover {
  background-color: #ecf5ff;
}

body.dark .voice-selector-trigger:hover {
  background-color: rgba(204, 221, 255, .1);
}

.selected-voice {
  flex: 1;
  font-size: 14px;
  color: #606266;
  text-align: left;
}

body.dark .selected-voice {
  color: var(--text-secondary);
}

.voice-selector-icon {
  font-size: 14px;
  color: #909399;
  transition: transform 0.3s;
}

body.dark .voice-selector-icon {
  color: var(--text-tertiary);
}

.voice-selector-container {
  padding: 4px;
  min-height: 200px;
  max-height: 800px;
}

.voice-param-item {
  /* margin-top: 10px; */
}

.voice-param-item .el-slider {
  padding: 0 4px;
  box-sizing: border-box;
}

.voice-param-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.voice-param-label {
  font-size: 12px;
  font-weight: 500;
  color: #606266;
}

.voice-param-value {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

body.dark .voice-param-label {
  color: var(--text-primary);
}

body.dark .voice-param-value {
  color: var(--text-primary);
}

/* 自定义滑块颜色 */
.voice-param-item :deep(.el-slider__bar) {
  background-color: #706cef8d;
}

.voice-param-item :deep(.el-slider__button) {
  border-color: #706cef9a;
}

body.dark .voice-param-item :deep(.el-slider__bar) {
  background-color: rgba(112, 108, 239, 0.542);
}

body.dark .voice-param-item :deep(.el-slider__button) {
  border-color: rgba(112, 108, 239, 0.46);
}

/* 情感选择器样式 */
.emotion-selector {
  width: 100%;
}

.emotion-selector :deep(.el-input__wrapper) {
  background-color: #f5f7fa;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05) inset !important;
}

body.dark .emotion-selector :deep(.el-input__wrapper) {
  background-color: rgba(204, 221, 255, .06);
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05) inset !important;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-textarea__inner) {
  font-size: 13px;
}

:deep(.el-input__inner) {
  font-size: 13px;
}

:deep(.el-slider__runway) {
  margin: 8px 0;
}

:deep(.el-slider__input) {
  width: 60px;
  margin-left: 10px;
}

/* Tab 样式 - 从 VideoGenerationPanel.vue 复制并调整 */
.generation-tabs {
  /* padding: 10px 10px 0 10px; */
  padding: 6px 0 0 0;
  /* border-bottom: 1px solid #8585852a; */
}

.asset-selector-tabs :deep(.el-tabs__header) {
  position: relative !important;
  margin: 0 !important;
}

.asset-selector-tabs :deep(.el-tabs__item) {
  color: #606266 !important;
  font-size: 14px !important;
  padding: 0 16px !important;
  height: 38px !important;
  line-height: 38px !important;
  transition: all 0.3s !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__item) {
  color: var(--text-secondary) !important;
}

.asset-selector-tabs :deep(.el-tabs__item.is-active) {
  color: #4f46e5 !important;
  font-weight: 500 !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__item.is-active) {
  color: #6366f1 !important;
}

.asset-selector-tabs :deep(.el-tabs__active-bar) {
  background-color: transparent !important;
  background-image: linear-gradient(90deg, transparent 0, transparent 0%,
      #4f46e5 0, #4f46e5 100%,
      transparent 0, transparent) !important;
  height: 2px !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__active-bar) {
  background-image: linear-gradient(90deg, transparent 0, transparent 0%,
      #6366f1 0, #6366f1 100%,
      transparent 0, transparent) !important;
}

.asset-selector-tabs :deep(.el-tabs__nav-wrap::after) {
  position: static !important;
  height: 1px !important;
  background-color: #e4e7ed !important;
}

body.dark .asset-selector-tabs :deep(.el-tabs__nav-wrap::after) {
  background-color: var(--border-color) !important;
}

.asset-selector-tabs :deep(.el-tabs__nav) {
  height: auto !important;
}

/* 辅助类 */
.ml-5 {
  margin-left: 5px;
}
</style>
