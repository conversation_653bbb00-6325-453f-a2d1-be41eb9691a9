<template>
  <div class="bg-audio-clip">

    <!-- 背景音乐轨道 -->
    <div class="bg-track-item" :class="{ 'active-track': isSelected }" v-if="bgClip" >
      <!-- 音轨内容区域 -->
      <div class="track-content" :style="{ width: `${totalDuration * timelineScale / 1000}px` }"
           @click="handleTrackClick">

        <!-- 背景音乐片段 -->
        <div v-if="bgClip"
             class="bg-audio-clip-item"
             v-context-menu="{ items: contextMenuItems, callback: handleContextMenuClick }"
             :style="{
               left: `${(bgClip.startTrackTime / 1000) * timelineScale}px`,
               width: `${((bgClip.endTime - bgClip.startTime) / 1000) * timelineScale}px`,
              //  backgroundColor: '#706cef33',
               zIndex: isSelected ? 10 : 1
             }"
             @mousedown="startDragClip($event)"
             :class="{ 'active': isSelected }"
        >
          <div class="clip-content">
            <!-- 音频谱可视化 -->
            <canvas
              v-if="bgClip.audioUrl"
              :ref="el => setClipCanvas(el)"
              class="audio-spectrum"
              width="100"
              height="30"
            ></canvas>
            <!-- 音量控制横线 -->
            <!-- <div
              class="volume-line"
              :style="{
                bottom: `${((bgClip.volume || 1) * 80)}%`,
                opacity: volumeLineVisible ? 0.4 : 0.2
              }"
              @mousedown="startVolumeLineDrag"
              @mouseenter="volumeLineVisible = true"
              @mouseleave="volumeLineVisible = false"
            ></div> -->
            <!-- <div class="clip-name">{{ bgClip.name || '背景音乐' }}</div> -->
            <div class="clip-duration">{{ formatDuration(bgClip.endTime - bgClip.startTime) }}</div>
          </div>
          <div class="clip-handle left" @mousedown.stop="startResizeClip('start', $event)"></div>
          <div class="clip-handle right" @mousedown.stop="startResizeClip('end', $event)"></div>

          <!-- 拖动时的时间提示 -->
          <div v-if="dragging.active" class="drag-time-indicator">
            {{ formatTime(Math.floor(bgClip.startTrackTime / 1000)) }}
          </div>

          <div class="handle-time-indicator">
            <!-- 编辑栏 - 使用 DropdownPanel 包装 -->

            <div class="edit-toolbar-trigger audio-btn-danger" @click="removeSelectedClip">
              <el-icon><Delete /></el-icon>
            </div>

          </div>
        </div>

        <!-- 添加背景音乐按钮 -->
        <div v-else class="add-bg-audio" @click="openFileSelector">
          <div class="add-bg-audio-content">
            <el-icon><Plus /></el-icon>
            <span>添加背景音乐</span>
          </div>
        </div>

        <!-- 播放进度指示器 -->
        <div class="track-progress-indicator" v-if="isPlaying"
          :style="{ left: `${(currentPlaybackTime * timelineScale)}px` }"></div>
      </div>
    </div>

    <!-- 时间轴标尺 -->
    <div class="timeline-ruler">
      <div class="ruler-content" :style="{ width: `${totalDuration * timelineScale / 1000}px` }">
        <!-- 主要刻度（带文本） -->
        <div v-for="second in Math.ceil(totalDuration / 1000 / rulerInterval)" :key="`major-${second}`" 
          class="ruler-mark"
          :style="{ left: `${second * rulerInterval * timelineScale}px` }">
          <div class="ruler-mark-line"></div>
          <div class="ruler-mark-text">{{ formatTime(second * rulerInterval) }}</div>
        </div>
        
        <!-- 次要刻度（不带文本） -->
        <template v-if="showMinorMark">
          <div v-for="second in Math.ceil(totalDuration / 1000)" :key="`minor-${second}`" 
              class="ruler-mark minor" 
              v-show="second % rulerInterval !== 0"
              :style="{ left: `${second * timelineScale + 14}px` }">
            <div class="ruler-mark-line"></div>
          </div>
        </template>
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, onBeforeUnmount } from 'vue';
import { Plus, VideoPlay, VideoPause, Delete, Setting } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import { bgAudioWaveformVisualizer } from '@/utils/BgAudioWaveformVisualizer.js';
import audioService from '@/services/audioService.js';
import DropdownPanel from '@/components/parent/DropdownPanel.vue';
import { useContextMenu } from '@/composables/useContextMenu.js';

// 右键菜单
const { createMenuItem } = useContextMenu();

// 组件属性
const props = defineProps({
  bgAudioTrack: {
    type: Object,
    default: () => null
  },
  timelineScale: {
    type: Number,
    default: 20
  },
  currentPlaybackTime: {
    type: Number,
    default: 0
  },
  isPlaying: {
    type: Boolean,
    default: false
  },
  totalDuration: {
    type: Number,
    default: 10000 // 默认总时长10秒
  },
  setBackgroundMusic: {
    type: Function,
    default: () => {}
  }
});

// 事件
const emit = defineEmits(['update:bgAudioTrack', 'play-audio', 'stop-audio', 'update:totalDuration', 'remove-audio']);

// 内部数据
const bgClip = ref(null);
const isSelected = ref(false);
const selectedClipVolume = ref(100);
const playingClipId = ref(null);
const isPlayingBgAudio = ref(false);
const showEditToolbar = ref(false);
const volumeLineVisible = ref(false);

// 右键菜单项配置
const contextMenuItems = [
  createMenuItem('删除', { icon: 'el-icon-delete', action: 'delete' })
];

// 处理右键菜单点击
const handleContextMenuClick = (item, index, target) => {
  if (item.action === 'delete') {
    removeSelectedClip();
  }
};

// 拖动状态
const dragging = ref({
  active: false,
  startX: 0,
  originalStartTime: 0,
  originalEndTime: 0,
  currentX: 0,
  timeOffset: 0,
  duration: 0
});

// 调整大小状态
const resizing = ref({
  active: false,
  type: '', // 'start' 或 'end'
  startX: 0,
  originalStartTime: 0,
  originalEndTime: 0,
  originalStartTrackTime: 0
});

// 音量横线拖动状态
const volumeLineDragging = ref({
  active: false,
  startY: 0,
  originalVolume: 0,
  clipRect: null
});

// 根据缩放比例计算标尺间隔
const rulerInterval = computed(() => {
  if (props.timelineScale <= 5) {
    return 30; // 每30秒一个标记
  } else if (props.timelineScale <= 10) {
    return 15; // 每15秒一个标记
  } else if (props.timelineScale <= 20) {
    return 5; // 每5秒一个标记
  } else if (props.timelineScale <= 40) {
    return 2; // 每2秒一个标记
  } else {
    return 1; // 每1秒一个标记
  }
});

// 判断是否显示次要刻度
const showMinorMark = computed(() => {
  return rulerInterval.value > 1;
});

// 监听 props 变化
watch(() => props.bgAudioTrack, (newBgAudioTrack) => {
  bgClip.value = newBgAudioTrack;
  if (newBgAudioTrack) {
    selectedClipVolume.value = newBgAudioTrack.volume || 100;
    isSelected.value = true;
  } else {
    isSelected.value = false;
  }
}, { deep: true, immediate: true });

// 监听播放进度变化，更新波形可视化器
watch(() => props.currentPlaybackTime, (newTime) => {
  if (bgAudioWaveformVisualizer.isReady()) {
    // 将播放时间转换为毫秒并传递给波形可视化器
    bgAudioWaveformVisualizer.updatePlaybackProgress(newTime * 1000);
  }
});

// 监听播放状态变化
watch(() => props.isPlaying, (newIsPlaying) => {
  if (bgAudioWaveformVisualizer.isReady()) {
    if (newIsPlaying) {
      // 如果开始播放，启动进度动画
      bgAudioWaveformVisualizer.startProgressAnimation();
    } else {
      // 如果停止播放，停止进度动画
      bgAudioWaveformVisualizer.stopProgressAnimation();
    }
  }
});

// 监听 bgClip 变化，确保在背景音乐更新后重新绘制波形
watch(bgClip, (newBgClip) => {
  console.log('背景音乐数据更新，检查是否需要重新绘制波形');

  emit('update:bgAudioTrack', newBgClip);

  // 延迟执行，确保DOM已更新
  setTimeout(() => {
    if (newBgClip && newBgClip.audioUrl) {
      console.log('背景音乐数据更新，更新波形可视化器');

      // 使用新的专用波形可视化工具更新音频信息
      if (bgAudioWaveformVisualizer.isReady()) {
        bgAudioWaveformVisualizer.updateAudioInfo(newBgClip);
      } else {
        console.log('波形可视化器未就绪，等待Canvas设置');
      }
    }
  }, 100);
}, { deep: true });

// 格式化时长
const formatDuration = (ms) => {
  const totalSeconds = Math.floor(ms / 1000);
  return audioService.formatTime(totalSeconds);
};

// 格式化时间
const formatTime = audioService.formatTime;

// 处理音轨点击事件
const handleTrackClick = () => {
  isSelected.value = true;
};

// 打开文件选择器
const openFileSelector = () => {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'audio/*';
  input.style.display = 'none';
  
  input.onchange = (e) => {
    const file = e.target.files[0];
    if (file) {
      uploadAudioFile(file);
    }
    document.body.removeChild(input);
  };
  
  document.body.appendChild(input);
  input.click();
};

// 上传音频文件
const uploadAudioFile = async (file) => {
  if (!file.type.startsWith('audio/')) {
    ElMessage.error('请选择有效的音频文件');
    return;
  }
  
  const loading = ElLoading.service({
    lock: true,
    text: '上传背景音乐中...',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  
  try {
    const audioUrl = URL.createObjectURL(file);
    const audioDuration = await audioService.getAudioDuration(file);
    
    const newClip = {
      id: `bg-clip-${Date.now()}`,
      name: file.name.split('.')[0],
      audioUrl: audioUrl,
      audioDuration: audioDuration,
      startTime: 0,
      endTime: audioDuration,
      startTrackTime: 0,
      volume: 1,
      file: file
    };
    
    bgClip.value = newClip;
    isSelected.value = true;

    // 检查是否需要扩展总时长
    if (audioDuration > props.totalDuration) {
      emit('update:totalDuration', audioDuration + 5000);
    }

    // 更新波形可视化器
    console.log('上传文件后更新背景音乐波形可视化器');
    if (bgAudioWaveformVisualizer.isReady()) {
      bgAudioWaveformVisualizer.updateAudioInfo(newClip);
    }

    ElMessage.success('背景音乐添加成功');
  } catch (error) {
    console.error('背景音乐处理失败:', error);
    ElMessage.error('背景音乐处理失败，请重试');
  } finally {
    loading.close();
  }
};

// 设置音频片段的canvas引用
const setClipCanvas = (el) => {
  if (bgClip.value && el) {
    console.log(`设置背景音乐Canvas ${bgClip.value.name}`);

    // 使用新的专用波形可视化工具
    const success = bgAudioWaveformVisualizer.setCanvas(el, bgClip.value);

    if (success) {
      console.log('背景音乐Canvas设置成功，波形已绘制');
    } else {
      console.error('背景音乐Canvas设置失败');
    }
  }
};

// 开始拖动音频片段
const startDragClip = (event) => {
  if (!bgClip.value) return;
  
  dragging.value = {
    active: true,
    startX: event.clientX,
    originalStartTrackTime: bgClip.value.startTrackTime,
    originalStartTime: bgClip.value.startTime,
    originalEndTime: bgClip.value.endTime,
    currentX: event.clientX,
    timeOffset: 0,
    duration: bgClip.value.endTime - bgClip.value.startTime
  };
  
  isSelected.value = true;
  
  document.addEventListener('mousemove', handleDragMove);
  document.addEventListener('mouseup', handleDragEnd);
  
  event.preventDefault();
  event.stopPropagation();
};

// 处理拖动过程中的鼠标移动
const handleDragMove = (event) => {
  if (!dragging.value.active || !bgClip.value) return;
  
  dragging.value.currentX = event.clientX;
  
  const deltaX = event.clientX - dragging.value.startX;
  const deltaTime = (deltaX / props.timelineScale) * 1000;
  
  let newStartTrackTime = Math.max(0, dragging.value.originalStartTrackTime + deltaTime);
  const clipDuration = dragging.value.duration;
  const newEndTrackTime = newStartTrackTime + clipDuration;
  
  if (newEndTrackTime > props.totalDuration) {
    emit('update:totalDuration', newEndTrackTime + 5000);
  }
  
  bgClip.value.startTrackTime = newStartTrackTime;
  dragging.value.timeOffset = deltaTime;
  
  // 更新音频谱
  if (bgAudioWaveformVisualizer.isReady()) {
    bgAudioWaveformVisualizer.updateAudioInfo(bgClip.value);
  }
};

// 结束拖动
const handleDragEnd = async () => {
  if (!dragging.value.active) return;

  dragging.value.active = false;

  document.removeEventListener('mousemove', handleDragMove);
  document.removeEventListener('mouseup', handleDragEnd);

  // 重新绘制音频谱
  if (bgAudioWaveformVisualizer.isReady()) {
    bgAudioWaveformVisualizer.redraw();
  }

  // 拖动结束时调用API更新背景音乐
  await callSetBackgroundMusicAPI();
};

// 开始调整音频片段大小
const startResizeClip = (type, event) => {
  if (!bgClip.value) return;

  resizing.value = {
    active: true,
    type,
    startX: event.clientX,
    originalStartTime: bgClip.value.startTime,
    originalEndTime: bgClip.value.endTime,
    originalStartTrackTime: bgClip.value.startTrackTime
  };

  document.addEventListener('mousemove', handleResizeMove);
  document.addEventListener('mouseup', handleResizeEnd);

  event.preventDefault();
  event.stopPropagation();
};

// 处理调整过程中的鼠标移动
const handleResizeMove = (event) => {
  if (!resizing.value.active || !bgClip.value) return;

  const deltaX = event.clientX - resizing.value.startX;
  const deltaTime = (deltaX / props.timelineScale) * 1000;

  const clipId = 'bg-0';
  let timeChanged = false;

  if (resizing.value.type === 'start') {
    let newStartTime = Math.max(0, resizing.value.originalStartTime + deltaTime);
    newStartTime = Math.min(newStartTime, resizing.value.originalEndTime - 500);

    const originalDuration = resizing.value.originalEndTime - resizing.value.originalStartTime;
    const newDuration = resizing.value.originalEndTime - newStartTime;
    const durationDiff = originalDuration - newDuration;
    const newStartTrackTime = resizing.value.originalStartTrackTime + durationDiff;

    if (newStartTrackTime >= 0) {
      bgClip.value.startTime = newStartTime;
      bgClip.value.startTrackTime = newStartTrackTime;
      timeChanged = true;
    }
  } else {
    let newEndTime = Math.max(resizing.value.originalStartTime + 500, resizing.value.originalEndTime + deltaTime);
    newEndTime = Math.min(newEndTime, bgClip.value.audioDuration);

    const newDuration = newEndTime - bgClip.value.startTime;
    const newEndTrackTime = bgClip.value.startTrackTime + newDuration;
    if (newEndTrackTime > props.totalDuration) {
      emit('update:totalDuration', newEndTrackTime + 5000);
    }

    bgClip.value.endTime = newEndTime;
    timeChanged = true;
  }

  if (timeChanged && bgAudioWaveformVisualizer.isReady()) {
    bgAudioWaveformVisualizer.updateAudioInfo(bgClip.value);
  }
};

// 结束调整
const handleResizeEnd = async () => {
  if (!resizing.value.active) return;

  resizing.value.active = false;

  document.removeEventListener('mousemove', handleResizeMove);
  document.removeEventListener('mouseup', handleResizeEnd);

  if (bgAudioWaveformVisualizer.isReady()) {
    bgAudioWaveformVisualizer.redraw();
  }

  // 调整结束时调用API更新背景音乐
  await callSetBackgroundMusicAPI();
};

// 播放选中的音频片段
const playSelectedClip = async () => {
  if (!bgClip.value) return;

  const loadingMessage = ElMessage({
    type: 'info',
    message: '正在加载背景音乐...',
    duration: 0
  });

  try {
    const success = await bgAudioWaveformVisualizer.playAudio(() => {
      // 播放结束回调
      playingClipId.value = null;
      isPlayingBgAudio.value = false;
      ElMessage.info(`${bgClip.value?.name || '背景音乐'} 播放完成`);
    });

    loadingMessage.close();

    if (success) {
      playingClipId.value = 'bg-0';
      isPlayingBgAudio.value = true;
      ElMessage.success(`正在播放: ${bgClip.value.name}`);
      emit('play-audio', bgClip.value);
    } else {
      isPlayingBgAudio.value = false;
      ElMessage.error(`播放失败: ${bgClip.value.name}`);
    }
  } catch (error) {
    loadingMessage.close();
    isPlayingBgAudio.value = false;
    console.error(`播放背景音乐时发生错误:`, error);
    ElMessage.error(`播放失败: ${error.message || '未知错误'}`);
  }
};

// 停止播放选中的音频片段
const stopSelectedClip = () => {
  if (playingClipId.value || isPlayingBgAudio.value) {
    try {
      const stopped = bgAudioWaveformVisualizer.stopAudio();

      if (!stopped) {
        setTimeout(() => {
          bgAudioWaveformVisualizer.stopAudio();
        }, 100);
      }
    } catch (error) {
      console.error(`停止背景音乐时发生错误:`, error);
    }

    playingClipId.value = null;
    isPlayingBgAudio.value = false;
    emit('stop-audio');
    ElMessage.info('已停止播放');
  }
};

// 删除选中的音频片段
const removeSelectedClip = async () => {
  ElMessageBox.confirm('确定要删除背景音乐吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    // 停止播放
    if (bgAudioWaveformVisualizer.isPlayingAudio()) {
      bgAudioWaveformVisualizer.stopAudio();
    }

    if (bgClip.value && bgClip.value.audioUrl && bgClip.value.audioUrl.startsWith('blob:')) {
      URL.revokeObjectURL(bgClip.value.audioUrl);
    }

    bgClip.value = null;
    isSelected.value = false;
    playingClipId.value = null;
    isPlayingBgAudio.value = false;
    
    callSetBackgroundMusicAPI();
  });
};

// 调用背景音乐设置API
const callSetBackgroundMusicAPI = async () => {
  if (props.setBackgroundMusic) {
    console.log('BgAudioClip: 调用背景音乐设置API，当前bgClip数据:', bgClip.value);
    console.log('BgAudioClip: setBackgroundMusic函数类型:', typeof props.setBackgroundMusic);
    try {
      await props.setBackgroundMusic(bgClip.value);
      console.log('BgAudioClip: 背景音乐设置API调用成功');
    } catch (error) {
      console.error('BgAudioClip: 调用背景音乐设置API失败:', error);
    }
  } else {
    console.log('BgAudioClip: 无法调用API - bgClip:', !!bgClip.value, 'setBackgroundMusic:', !!props.setBackgroundMusic);
  }
};

// 更新选中音频片段的音量
const updateSelectedClipVolume = async () => {
  if (bgClip.value) {
    bgClip.value.volume = selectedClipVolume.value;
    // 音量变化时调用API
    await callSetBackgroundMusicAPI();
  }
};

// 开始拖动音量横线
const startVolumeLineDrag = (event) => {
  if (!bgClip.value) return;

  const clipElement = event.target.closest('.bg-audio-clip-item');
  if (!clipElement) return;

  volumeLineDragging.value = {
    active: true,
    startY: event.clientY,
    originalVolume: bgClip.value.volume || 1,
    clipRect: clipElement.getBoundingClientRect()
  };

  document.addEventListener('mousemove', handleVolumeLineDragMove);
  document.addEventListener('mouseup', handleVolumeLineDragEnd);

  event.preventDefault();
  event.stopPropagation();
};

// 处理音量横线拖动过程中的鼠标移动
const handleVolumeLineDragMove = (event) => {
  if (!volumeLineDragging.value.active || !bgClip.value) return;

  const deltaY = volumeLineDragging.value.startY - event.clientY; // 向上为正
  const clipHeight = volumeLineDragging.value.clipRect.height;
  const volumeChange = deltaY / clipHeight; // 计算音量变化比例

  let newVolume = volumeLineDragging.value.originalVolume + volumeChange;
  newVolume = Math.max(0, Math.min(1, newVolume)); // 限制在0-1之间

  bgClip.value.volume = newVolume;
  selectedClipVolume.value = newVolume; // 同步更新滑块值

  // 实时更新波形可视化器的音量显示
  if (bgAudioWaveformVisualizer.isReady()) {
    bgAudioWaveformVisualizer.updateVolume(newVolume);
  }
};

// 结束音量横线拖动
const handleVolumeLineDragEnd = async () => {
  if (!volumeLineDragging.value.active) return;

  volumeLineDragging.value.active = false;

  document.removeEventListener('mousemove', handleVolumeLineDragMove);
  document.removeEventListener('mouseup', handleVolumeLineDragEnd);

  // 确保波形可视化器已更新到最终音量
  if (bgAudioWaveformVisualizer.isReady() && bgClip.value) {
    bgAudioWaveformVisualizer.updateAudioInfo(bgClip.value);
  }

  // 拖动结束时调用API更新音量
  await callSetBackgroundMusicAPI();
};

// 组件挂载时
onMounted(() => {
  if (props.bgAudioTrack) {
    bgClip.value = props.bgAudioTrack;
    selectedClipVolume.value = props.bgAudioTrack.volume || 1;
    isSelected.value = true;

    console.log('组件挂载时设置背景音乐数据');
  }
});

// 组件卸载前清理资源
onBeforeUnmount(() => {
  // 停止播放
  if (bgAudioWaveformVisualizer.isPlayingAudio()) {
    bgAudioWaveformVisualizer.stopAudio();
  }

  if (playingClipId.value) {
    playingClipId.value = null;
  }

  isPlayingBgAudio.value = false;

  // 清理背景音乐波形可视化器
  bgAudioWaveformVisualizer.cleanup();

  if (bgClip.value && bgClip.value.audioUrl && bgClip.value.audioUrl.startsWith('blob:')) {
    URL.revokeObjectURL(bgClip.value.audioUrl);
  }

  document.removeEventListener('mousemove', handleDragMove);
  document.removeEventListener('mouseup', handleDragEnd);
  document.removeEventListener('mousemove', handleResizeMove);
  document.removeEventListener('mouseup', handleResizeEnd);
  document.removeEventListener('mousemove', handleVolumeLineDragMove);
  document.removeEventListener('mouseup', handleVolumeLineDragEnd);
});
</script>

<style scoped>
.bg-audio-clip {
  width: 100%;
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 6px;
  min-width: 100%;
  position: relative;
  text-align: left;
}

body.dark .bg-audio-clip {
  background-color: var(--bg-secondary-video);
}

/* 时间轴标尺 */
.timeline-ruler {
  height: 20px;
  position: relative;
  overflow: hidden;
}

.ruler-content {
  position: relative;
  height: 100%;
}

.ruler-mark {
  position: absolute;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.ruler-mark.minor {
  height: 50%;
  top: 50%;
}

.ruler-mark-line {
  width: 1px;
  height: 8px;
  background-color: #c0c4cc;
}

.ruler-mark.minor .ruler-mark-line {
  height: 4px;
  background-color: #e0e0e0;
}

.ruler-mark-text {
  font-size: 10px;
  color: #909399;
}

body.dark .ruler-mark-text {
  color: var(--text-secondary);
}

/* 背景音乐轨道样式 */
.bg-track-item {
  display: flex;
  height: 100%;
  border-radius: 4px;
  overflow: hidden;
  transition: border-color 0.2s;
}

.bg-track-item.active-track {
  background-color: rgba(112, 108, 239, 0.08);
  box-shadow: 0 0 8px rgba(112, 108, 239, 0.3);
}

body.dark .bg-track-item.active-track {
  background-color: rgba(112, 108, 239, 0.12);
  box-shadow: 0 0 8px rgba(112, 108, 239, 0.2);
}

.track-content {
  flex: 1;
  position: relative;
  height: 34px;
  background-color: #f5f7fa89;
  border-top: 0.5px solid #69696933;
}

body.dark .track-content {
  background-color: #1e1e1e92;
}

/* 背景音乐片段样式 */
.bg-audio-clip-item {
  position: absolute;
  margin: 1px 0px;
  height: 32px;
  border-radius: 4px;
  overflow: visible;
  cursor: move;
  transition: box-shadow 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  /* box-shadow: 0 0 0 1px #706cef30; */
  background-color: #706cef27;
}

body.dark .bg-audio-clip-item {
  background-color: rgba(64, 158, 255, 0.2);
}

.bg-audio-clip-item:hover .edit-toolbar-trigger {
  display: flex;
}

.bg-audio-clip-item.active {
  /* box-shadow: 0 0 1px 1px #706cef79; */
  /* z-index: 10 !important; */
}

body.dark .bg-audio-clip-item.active {
  /* box-shadow: 0 0 1px 1px #706cef79; */
}

.clip-content {
  width: 100%;
  height: 100%;
  padding: 0 8px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
  position: relative; /* 为绝对定位的canvas提供定位上下文 */
}

.clip-name {
  font-size: 10px;
  color: #303133bb;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 70%;
}

body.dark .clip-name {
  color: #f0f0f0ad;
}

.clip-duration {
  font-size: 10px;
  color: #606266;
  margin-left: 4px;
}

body.dark .clip-duration {
  color: var(--text-secondary);
}

/* 调整手柄 */
.clip-handle {
  position: absolute;
  width: 4px;
  height: 100%;
  top: 0;
  background-color: #706cefbc;
  cursor: ew-resize;
  opacity: 0;
  transition: opacity 0.2s;
  border-radius: 4px;
}

/* body.dark .clip-handle {
  background-color: rgba(255, 255, 255, 0.332);
} */

.bg-audio-clip-item:hover .clip-handle {
  opacity: 1;
}

.clip-handle.left {
  left: 0;
}

.clip-handle.right {
  right: 0;
}

/* 拖动时的时间提示 */
.drag-time-indicator {
  position: absolute;
  top: -20px;
  left: 0;
  background-color: #706cef;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  pointer-events: none;
  z-index: 20;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

body.dark .drag-time-indicator {
  background-color: #706cef;
}

/* 添加背景音乐按钮 */
.add-bg-audio {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #c0c4cc;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-bg-audio:hover {
  border-color: #706cef;
  background-color: rgba(112, 108, 239, 0.05);
}

.add-bg-audio-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6px;
  color: #909399;
  font-size: 12px;
}

body.dark .add-bg-audio-content {
  color: var(--text-secondary);
}

/* 播放进度指示器 */
.track-progress-indicator {
  position: absolute;
  top: 0;
  height: 100%;
  width: 2px;
  background-color: #575dff;
  z-index: 15;
  pointer-events: none;
  transition: left 0.1s linear;
}

body.dark .track-progress-indicator {
  background-color: #575dff;
}

.handle-time-indicator {
  position: absolute;
  top: 5px;
  left: 10px;
}

/* 编辑栏触发器 */
.edit-toolbar-trigger {
  display: none;
  align-items: center;
  padding: 4px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #f0f2f5;
  color: #606266;
  font-size: 10px;
  border: 1px solid #dcdfe6;
}

.edit-toolbar-trigger:hover {
  background-color: #e6e8eb;
  border-color: #c0c4cc;
}

.edit-icon {
  font-size: 14px;
}

.edit-text {
  font-weight: 500;
}

body.dark .edit-toolbar-trigger {
  background-color: #2c3e50;
  color: #e0e0e0;
  border-color: #4a5568;
}

body.dark .edit-toolbar-trigger:hover {
  background-color: #34495e;
  border-color: #5a6c7d;
}

/* 编辑栏 */
.bg-audio-edit-toolbar {
  width: auto;
  padding: 8px;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

body.dark .bg-audio-edit-toolbar {
  background-color: #2c3e50;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.toolbar-section {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 200px;
}

.volume-label {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
}

body.dark .volume-label {
  color: var(--text-secondary);
}

.volume-slider {
  width: 120px;
}

.volume-value {
  font-size: 12px;
  color: #606266;
  min-width: 36px;
  text-align: right;
  white-space: nowrap;
}

body.dark .volume-value {
  color: var(--text-secondary);
}

/* 自定义按钮样式 */
.audio-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  padding: 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  background-color: #f0f2f5;
  border-radius: 12px;
  color: #606266;
  margin: 0 4px;
}

.audio-btn:hover {
  background-color: #e6e8eb;
  transform: translateY(-1px);
}

.audio-btn:active {
  transform: translateY(0);
}

.audio-btn-primary {
  background-color: #706cef;
  color: white;
}

.audio-btn-primary:hover {
  background-color: #8a87f0;
}

.audio-btn-warning {
  background-color: #e6a23c;
  color: white;
}

.audio-btn-warning:hover {
  background-color: #ebb563;
}

.audio-btn-danger {
  background-color: #f56c6c;
  color: white;
}

.audio-btn-danger:hover {
  background-color: #f78989;
}

/* 暗色模式按钮样式 */
body.dark .audio-btn {
  background-color: #2c3e50;
  color: #e0e0e0;
}

body.dark .audio-btn:hover {
  background-color: #34495e;
}

body.dark .audio-btn-primary {
  background-color: #706cef;
  color: white;
}

body.dark .audio-btn-primary:hover {
  background-color: #8a87f0;
}

body.dark .audio-btn-warning {
  background-color: #e6a23c;
  color: white;
}

body.dark .audio-btn-warning:hover {
  background-color: #ebb563;
}

body.dark .audio-btn-danger {
  background-color: #f56c6c;
  color: white;
}

body.dark .audio-btn-danger:hover {
  background-color: #f78989;
}

/* 音频谱样式 */
.audio-spectrum {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 1; /* 提高透明度 */
  /* 提高层级 */
  /* z-index: 2;  */
  overflow: hidden;
}

/* 音量控制横线样式 */
.volume-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #706cef;
  cursor: ns-resize;
  transition: opacity 0.2s ease, box-shadow 0.2s ease;
  border-radius: 1px;
}

.volume-line:hover {
  height: 2px;
  box-shadow: 0 0 1px rgba(112, 108, 239, 0.6);
}

.volume-line:active {
  height: 2px;
  box-shadow: 0 0 1px rgba(112, 108, 239, 0.8);
}

body.dark .volume-line {
  background-color: #8a87f0;
}

body.dark .volume-line:hover {
  box-shadow: 0 0 1px rgba(138, 135, 240, 0.6);
}

body.dark .volume-line:active {
  box-shadow: 0 0 1px rgba(138, 135, 240, 0.8);
}
</style>
