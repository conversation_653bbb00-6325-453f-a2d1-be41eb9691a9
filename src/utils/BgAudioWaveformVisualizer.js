/**
 * 背景音乐波形可视化工具
 * 专门为 BgAudioClip 组件设计的简化版波形绘制工具
 * 包含音频播放和停止功能，支持实时播放进度显示
 */
class BgAudioWaveformVisualizer {
  constructor() {
    // Canvas 元素和上下文存储
    this.canvas = null;
    this.ctx = null;

    // 音频信息存储
    this.audioInfo = null;

    // 绘制状态
    this.isInitialized = false;

    // 音频播放相关
    this.audioContext = null;
    this.audioBuffer = null;
    this.audioSource = null;
    this.gainNode = null;
    this.isPlaying = false;
    this.playStartTime = 0;
    this.audioStartTime = 0;
    this.audioDuration = 0;
    this.onEndedCallback = null;

    // 播放进度相关
    this.currentPlaybackTime = 0; // 当前播放时间（毫秒）
    this.animationFrameId = null;
    this.lastProgressUpdateTime = 0;
    this.progressUpdateThreshold = 16; // 约60fps的更新频率

    // 静态波形数据缓存
    this.staticWaveformData = null;

    // 初始化音频上下文
    this.initAudioContext();

    console.log('BgAudioWaveformVisualizer 初始化完成');
  }

  /**
   * 初始化音频上下文
   */
  initAudioContext() {
    try {
      const AudioContext = window.AudioContext || window.webkitAudioContext;
      this.audioContext = new AudioContext();
      console.log('BgAudioWaveformVisualizer: AudioContext 创建成功');
    } catch (error) {
      console.error('BgAudioWaveformVisualizer: AudioContext 创建失败:', error);
    }
  }

  /**
   * 恢复音频上下文
   */
  async resumeAudioContext() {
    if (!this.audioContext) {
      this.initAudioContext();
      return !!this.audioContext;
    }

    if (this.audioContext.state === 'suspended') {
      try {
        await this.audioContext.resume();
        console.log('BgAudioWaveformVisualizer: AudioContext 恢复成功');
        return true;
      } catch (error) {
        console.error('BgAudioWaveformVisualizer: AudioContext 恢复失败:', error);
        return false;
      }
    }

    return true;
  }

  /**
   * 加载音频文件
   * @param {string} url - 音频文件URL
   * @returns {Promise<AudioBuffer>} 音频缓冲区
   */
  async loadAudioFile(url) {
    if (!this.audioContext) {
      throw new Error('AudioContext 未初始化');
    }

    try {
      console.log('BgAudioWaveformVisualizer: 开始加载音频:', url);
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status}`);
      }

      const arrayBuffer = await response.arrayBuffer();
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);

      console.log('BgAudioWaveformVisualizer: 音频加载成功, 时长:', audioBuffer.duration, '秒');
      return audioBuffer;
    } catch (error) {
      console.error('BgAudioWaveformVisualizer: 音频加载失败:', error);
      throw error;
    }
  }

  /**
   * 设置 Canvas 元素
   * @param {HTMLCanvasElement} canvasElement - Canvas 元素
   * @param {Object} audioClip - 音频片段数据
   */
  setCanvas(canvasElement, audioClip) {
    if (!canvasElement) {
      console.warn('BgAudioWaveformVisualizer: Canvas 元素为空');
      return false;
    }

    this.canvas = canvasElement;
    this.ctx = canvasElement.getContext('2d');

    if (!this.ctx) {
      console.error('BgAudioWaveformVisualizer: 无法获取 Canvas 上下文');
      return false;
    }

    // 设置 Canvas 渲染质量
    this.ctx.imageSmoothingEnabled = false; // 禁用图像平滑，保持像素清晰

    // 存储音频信息
    if (audioClip) {
      this.audioInfo = {
        audioUrl: audioClip.audioUrl,
        name: audioClip.name || '背景音乐',
        startTime: audioClip.startTime || 0,
        endTime: audioClip.endTime || audioClip.audioDuration,
        audioDuration: audioClip.audioDuration,
        volume: audioClip.volume || 100
      };
    }

    this.isInitialized = true;
    
    console.log('BgAudioWaveformVisualizer: Canvas 设置成功', {
      width: this.canvas.width,
      height: this.canvas.height,
      audioInfo: this.audioInfo
    });

    // 立即绘制波形
    this.drawWaveform();
    
    return true;
  }

  /**
   * 更新音频信息
   * @param {Object} audioClip - 音频片段数据
   */
  updateAudioInfo(audioClip) {
    if (!audioClip) return;

    // 检查音量是否发生变化
    const oldVolume = this.audioInfo ? this.audioInfo.volume : null;
    const newVolume = audioClip.volume || 100;
    const volumeChanged = oldVolume !== null && oldVolume !== newVolume;

    this.audioInfo = {
      audioUrl: audioClip.audioUrl,
      name: audioClip.name || '背景音乐',
      startTime: audioClip.startTime || 0,
      endTime: audioClip.endTime || audioClip.audioDuration,
      audioDuration: audioClip.audioDuration,
      volume: newVolume
    };

    console.log('BgAudioWaveformVisualizer: 音频信息已更新', this.audioInfo);

    // 如果音量发生变化，清除静态波形缓存以强制重新生成
    if (volumeChanged) {
      console.log('BgAudioWaveformVisualizer: 音量发生变化，清除静态波形缓存');
      this.staticWaveformData = null;
    }

    // 重新绘制波形
    this.drawWaveform();
  }

  /**
   * 更新音量并重新绘制波形
   * @param {number} volume - 新的音量值 (0-1)
   */
  updateVolume(volume) {
    if (!this.audioInfo) return;

    // 检查音量是否真的发生了变化
    const oldVolume = this.audioInfo.volume;
    const newVolume = volume;

    if (oldVolume === newVolume) return;

    console.log('BgAudioWaveformVisualizer: 音量更新', { oldVolume, newVolume });

    // 更新音量
    this.audioInfo.volume = newVolume;

    // 清除静态波形缓存以强制重新生成
    this.staticWaveformData = null;

    // 重新绘制波形
    this.drawWaveform();
  }

  /**
   * 绘制波形 - 只绘制 startTime 到 endTime 部分
   */
  drawWaveform() {
    if (!this.isInitialized || !this.canvas || !this.ctx) {
      console.warn('BgAudioWaveformVisualizer: 未初始化，无法绘制波形');
      return;
    }

    // 获取 Canvas 的实际显示尺寸
    const rect = this.canvas.getBoundingClientRect();
    const displayWidth = rect.width;
    const displayHeight = rect.height;

    // 获取设备像素比
    const devicePixelRatio = window.devicePixelRatio || 1;

    // 设置 Canvas 的实际像素尺寸以匹配显示尺寸
    const actualWidth = Math.floor(displayWidth * devicePixelRatio);
    const actualHeight = Math.floor(displayHeight * devicePixelRatio);

    // 只有当尺寸发生变化时才调整 Canvas 尺寸
    if (this.canvas.width !== actualWidth || this.canvas.height !== actualHeight) {
      this.canvas.width = actualWidth;
      this.canvas.height = actualHeight;

      // 缩放上下文以匹配设备像素比
      this.ctx.scale(devicePixelRatio, devicePixelRatio);

      console.log('BgAudioWaveformVisualizer: Canvas 尺寸已调整', {
        displaySize: `${displayWidth}x${displayHeight}`,
        actualSize: `${actualWidth}x${actualHeight}`,
        devicePixelRatio
      });

      // 尺寸变化时清除静态波形缓存
      this.staticWaveformData = null;
    }

    const width = displayWidth;
    const height = displayHeight;

    if (width <= 0 || height <= 0) {
      console.warn('BgAudioWaveformVisualizer: Canvas 尺寸无效', { width, height });
      return;
    }

    // 清除画布
    this.ctx.clearRect(0, 0, width, height);

    // 绘制静态波形
    this.drawStaticWaveform(width, height);

    // 绘制播放进度线
    this.drawProgressLine(width, height);

    console.log('BgAudioWaveformVisualizer: 波形绘制完成', {
      width,
      height,
      audioInfo: this.audioInfo,
      currentPlaybackTime: this.currentPlaybackTime
    });

  }

  /**
   * 绘制静态波形
   * @param {number} width - Canvas 宽度
   * @param {number} height - Canvas 高度
   */
  drawStaticWaveform(width, height) {
    // 如果已有缓存的静态波形数据，直接使用
    if (this.staticWaveformData &&
        this.staticWaveformData.width === width &&
        this.staticWaveformData.height === height) {
      this.renderCachedWaveform();
      return;
    }

    // 生成新的静态波形数据
    this.generateStaticWaveformData(width, height);
    this.renderCachedWaveform();
  }

  /**
   * 生成静态波形数据
   * @param {number} width - Canvas 宽度
   * @param {number} height - Canvas 高度
   */
  generateStaticWaveformData(width, height) {
    // 固定柱子参数 - 不随 Canvas 宽度变化
    const barWidth = 2; // 固定柱子宽度为 2px
    const gap = 1; // 固定间隙为 1px
    const totalBarWidth = barWidth + gap;
    const barCount = Math.floor(width / totalBarWidth); // Canvas 越宽，柱子数量越多

    // 计算音频片段在原始音频中的起始和结束位置比例
    let startRatio = 0;
    let endRatio = 1;

    if (this.audioInfo) {
      // 获取音频片段信息
      const clipDuration = this.audioInfo.endTime - this.audioInfo.startTime; // 音频片段总时长(毫秒)
      const audioDuration = this.audioInfo.audioDuration || clipDuration; // 音频原始时长

      // 计算音频片段在原始音频中的起始和结束位置比例
      startRatio = this.audioInfo.startTime / audioDuration;
      endRatio = this.audioInfo.endTime / audioDuration;
    }

    // 生成一个随机种子，但对于同一个音频片段保持一致
    const seed = this.audioInfo ? this.audioInfo.name.split('').reduce((a, b) => a + b.charCodeAt(0), 0) : 456;

    // 计算每个柱子代表的时间区间比例
    const ratioPerBar = (endRatio - startRatio) / barCount;

    // 计算中心线位置
    const centerY = height / 2;

    const bars = [];
    for (let i = 0; i < barCount; i++) {
      // 计算当前柱子在原始音频中的位置比例
      const positionRatio = startRatio + i * ratioPerBar;

      // 使用一致的种子和位置比例生成伪随机波形
      const phase = seed / 100;
      const position = positionRatio * 100; // 将比例放大，使波形变化更明显

      // 使用更高频率和随机性，减少平滑效果，创造更锐利的波形
      const randomFactor = Math.sin(position * 2.3 + seed) * 0.2; // 添加随机性
      const heightFactor = 0.2 +
                           0.5 * Math.sin(position * 1.2 + phase) +
                           0.3 * Math.sin(position * 2.8 + phase * 0.3) +
                           randomFactor;

      // 根据音量调整波形高度 - audioInfo.volume 范围通常是 0-100
      const volumeMultiplier = (this.audioInfo ? (this.audioInfo.volume || 1) : 1) * 0.8;
      const adjustedHeightFactor = Math.abs(heightFactor) * volumeMultiplier;

      // 计算从中心线向上下延伸的高度，最大为高度的40%
      const maxBarHeight = height * 0.4;
      const barHeight = maxBarHeight * Math.min(1, Math.max(0.1, adjustedHeightFactor));

      // 根据位置设置不同的颜色 - 参考原有样式但使用更适合背景音乐的颜色
      const hue = 240 - Math.abs(heightFactor) * 60; // 从蓝色到红色的渐变
      const color = `hsla(${hue}, 90%, 60%, 0.9)`;

      bars.push({
        x: Math.floor(i * totalBarWidth),
        centerY: Math.floor(centerY),
        width: Math.floor(barWidth),
        height: Math.floor(barHeight),
        color: color
      });
    }

    // 缓存静态波形数据
    this.staticWaveformData = {
      width,
      height,
      bars,
      barWidth,
      totalBarWidth,
      centerY
    };
  }

  /**
   * 渲染缓存的波形数据
   */
  renderCachedWaveform() {
    if (!this.staticWaveformData) return;

    const { bars } = this.staticWaveformData;

    bars.forEach(bar => {
      this.ctx.fillStyle = bar.color;

      // 绘制从中心线向上的竖线
      const upY = bar.centerY - bar.height;
      this.ctx.fillRect(bar.x, upY, bar.width, bar.height);

      // 绘制从中心线向下的竖线
      this.ctx.fillRect(bar.x, bar.centerY, bar.width, bar.height);

      // 添加上下两端的圆角效果
      if (bar.height > 2 && bar.width >= 2) {
        const radius = bar.width / 2;

        // 上端圆角
        this.ctx.beginPath();
        this.ctx.arc(bar.x + radius, upY, radius, 0, Math.PI, true);
        this.ctx.fill();

        // 下端圆角
        this.ctx.beginPath();
        this.ctx.arc(bar.x + radius, bar.centerY + bar.height, radius, Math.PI, 2 * Math.PI, true);
        this.ctx.fill();
      }
    });
  }

  /**
   * 绘制播放进度线
   * @param {number} width - Canvas 宽度
   * @param {number} height - Canvas 高度
   */
  drawProgressLine(width, height) {
    if (!this.audioInfo) return;

    // 计算当前播放进度在音频片段中的位置
    const clipDuration = this.audioInfo.endTime - this.audioInfo.startTime; // 音频片段时长
    let currentProgress = this.currentPlaybackTime; // 当前播放时间（相对于音频片段开始）

    // 如果不在播放状态，不显示进度线
    if (!this.isPlaying) return;

    // 确保进度在有效范围内
    if (currentProgress < 0 || currentProgress > clipDuration) return;

    // 计算进度线在 Canvas 中的 x 位置
    const progressRatio = currentProgress / clipDuration;
    const progressX = Math.floor(progressRatio * width);

    // 确保进度线在Canvas范围内
    if (progressX < 0 || progressX > width) return;

    // 绘制进度线
    this.ctx.strokeStyle = '#ff4444';
    this.ctx.lineWidth = 2;
    this.ctx.beginPath();
    this.ctx.moveTo(progressX, 0);
    this.ctx.lineTo(progressX, height);
    this.ctx.stroke();

    // 绘制进度线顶部的小圆点
    this.ctx.fillStyle = '#ff4444';
    this.ctx.beginPath();
    this.ctx.arc(progressX, 4, 3, 0, 2 * Math.PI);
    this.ctx.fill();
  }

  /**
   * 重新绘制波形（外部调用接口）
   */
  redraw() {
    this.drawWaveform();
  }

  /**
   * 更新播放进度
   * @param {number} currentTimeMs - 当前播放时间（毫秒，相对于整个时间轴）
   */
  updatePlaybackProgress(currentTimeMs) {
    if (!this.audioInfo) return;

    // 计算相对于音频片段的播放时间
    const trackStartTime = this.audioInfo.startTrackTime || 0;
    const relativeTime = currentTimeMs - trackStartTime;

    // 只有当播放时间在音频片段范围内时才更新
    const clipDuration = this.audioInfo.endTime - this.audioInfo.startTime;
    if (relativeTime >= 0 && relativeTime <= clipDuration) {
      this.currentPlaybackTime = relativeTime;

      // 性能优化：限制更新频率
      const now = Date.now();
      if (now - this.lastProgressUpdateTime >= this.progressUpdateThreshold) {
        this.lastProgressUpdateTime = now;
        // 重新绘制波形（包含进度线）
        this.drawWaveform();
      }
    }
  }

  /**
   * 开始播放进度动画
   */
  startProgressAnimation() {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
    }

    const animate = () => {
      if (this.isPlaying) {
        // 计算当前播放时间（相对于音频片段开始的时间）
        const elapsed = Date.now() - this.playStartTime;
        this.currentPlaybackTime = elapsed;

        // 重新绘制波形（包含进度线）
        this.drawWaveform();

        // 继续动画
        this.animationFrameId = requestAnimationFrame(animate);
      }
    };

    this.animationFrameId = requestAnimationFrame(animate);
  }

  /**
   * 停止播放进度动画
   */
  stopProgressAnimation() {
    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }
  }

  /**
   * 播放音频
   * @param {Function} onEnded - 播放结束回调
   * @returns {Promise<boolean>} 是否成功开始播放
   */
  async playAudio(onEnded) {
    if (!this.audioInfo || !this.audioInfo.audioUrl) {
      console.error('BgAudioWaveformVisualizer: 无法播放，音频信息不存在');
      if (typeof onEnded === 'function') {
        setTimeout(onEnded, 100);
      }
      return false;
    }

    // 确保AudioContext处于活动状态
    if (!await this.resumeAudioContext()) {
      console.error('BgAudioWaveformVisualizer: 无法恢复AudioContext');
      if (typeof onEnded === 'function') {
        setTimeout(onEnded, 100);
      }
      return false;
    }

    // 停止当前播放（如果正在播放）
    this.stopAudio();

    try {
      // 加载音频文件
      this.audioBuffer = await this.loadAudioFile(this.audioInfo.audioUrl);

      // 创建音频源节点
      this.audioSource = this.audioContext.createBufferSource();
      this.audioSource.buffer = this.audioBuffer;

      // 创建增益节点（用于控制音量）
      this.gainNode = this.audioContext.createGain();
      const volume = (this.audioInfo.volume || 100) / 100;
      this.gainNode.gain.value = volume;

      // 连接节点
      this.audioSource.connect(this.gainNode);
      this.gainNode.connect(this.audioContext.destination);

      // 计算播放参数
      const startTime = this.audioInfo.startTime / 1000; // 毫秒转秒
      const endTime = this.audioInfo.endTime / 1000; // 毫秒转秒
      const duration = endTime - startTime;

      console.log('BgAudioWaveformVisualizer: 播放参数', {
        startTime,
        endTime,
        duration,
        volume
      });

      // 保存回调函数
      this.onEndedCallback = onEnded;

      // 设置播放结束回调
      this.audioSource.onended = () => {
        console.log('BgAudioWaveformVisualizer: 音频播放结束');
        this.isPlaying = false;
        this.audioSource = null;
        this.gainNode = null;

        // 停止播放进度动画
        this.stopProgressAnimation();

        if (typeof this.onEndedCallback === 'function') {
          this.onEndedCallback();
        }
      };

      // 记录播放状态
      this.isPlaying = true;
      this.playStartTime = Date.now();
      this.audioStartTime = startTime;
      this.audioDuration = duration;
      this.currentPlaybackTime = 0; // 重置播放进度

      // 开始播放进度动画
      this.startProgressAnimation();

      // 开始播放
      if (endTime < this.audioBuffer.duration) {
        this.audioSource.start(0, startTime, duration);
      } else {
        this.audioSource.start(0, startTime);
      }

      console.log('BgAudioWaveformVisualizer: 音频播放开始');
      return true;

    } catch (error) {
      console.error('BgAudioWaveformVisualizer: 播放失败:', error);
      this.isPlaying = false;
      this.audioSource = null;
      this.gainNode = null;

      if (typeof onEnded === 'function') {
        setTimeout(onEnded, 100);
      }
      return false;
    }
  }

  /**
   * 停止音频播放
   * @returns {boolean} 是否成功停止
   */
  stopAudio() {
    console.log('BgAudioWaveformVisualizer: 停止音频播放');

    let stopped = false;

    // 停止播放进度动画
    this.stopProgressAnimation();

    if (this.audioSource) {
      try {
        this.audioSource.stop();
        this.audioSource.onended = null;
        this.audioSource.disconnect();
        stopped = true;
        console.log('BgAudioWaveformVisualizer: AudioSource 已停止');
      } catch (error) {
        console.warn('BgAudioWaveformVisualizer: 停止 AudioSource 失败:', error);
      }
      this.audioSource = null;
    }

    if (this.gainNode) {
      try {
        this.gainNode.disconnect();
      } catch (error) {
        console.warn('BgAudioWaveformVisualizer: 断开 GainNode 失败:', error);
      }
      this.gainNode = null;
    }

    this.isPlaying = false;
    this.onEndedCallback = null;
    this.currentPlaybackTime = 0; // 重置播放进度

    // 重新绘制波形（移除进度线）
    this.drawWaveform();

    return stopped;
  }

  /**
   * 清理资源
   */
  cleanup() {
    // 停止播放
    this.stopAudio();

    // 停止播放进度动画
    this.stopProgressAnimation();

    // 清理Canvas相关
    this.canvas = null;
    this.ctx = null;
    this.audioInfo = null;
    this.isInitialized = false;

    // 清理音频相关
    this.audioBuffer = null;

    // 清理播放进度相关
    this.currentPlaybackTime = 0;
    this.staticWaveformData = null;

    console.log('BgAudioWaveformVisualizer: 资源已清理');
  }

  /**
   * 检查是否已初始化
   */
  isReady() {
    return this.isInitialized && this.canvas && this.ctx;
  }

  /**
   * 检查是否正在播放
   */
  isPlayingAudio() {
    return this.isPlaying;
  }
}

// 创建单例实例
const bgAudioWaveformVisualizer = new BgAudioWaveformVisualizer();

export { bgAudioWaveformVisualizer };
export default bgAudioWaveformVisualizer;
